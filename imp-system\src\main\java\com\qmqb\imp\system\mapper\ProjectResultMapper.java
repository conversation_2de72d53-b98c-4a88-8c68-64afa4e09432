package com.qmqb.imp.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.ProjectResult;
import com.qmqb.imp.system.domain.vo.ProjectResultVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目成果表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ProjectResultMapper extends BaseMapperPlus<ProjectResultMapper, ProjectResult, ProjectResultVo> {

    /**
     * 分页查询项目成果列表（关联业务类型表）
     * @param page 分页对象
     * @param queryWrapper 查询条件
     * @param businessCategoryMajor 业务大类
     * @param businessCategoryMinor 业务小类
     * @param orderByColumn 排序字段
     * @param orderByColumns 排序字段数组
     * @param isAscArray 排序方向数组
     * @return 项目成果列表
     */
    Page<ProjectResultVo> selectVoPageWithBusinessType(Page<ProjectResult> page,
                                                      @Param(Constants.WRAPPER) Wrapper<ProjectResult> queryWrapper,
                                                      @Param("businessCategoryMajor") String businessCategoryMajor,
                                                      @Param("businessCategoryMinor") String businessCategoryMinor,
                                                      @Param("orderByColumn") String orderByColumn,
                                                      @Param("orderByColumns") String[] orderByColumns,
                                                      @Param("isAscArray") String[] isAscArray);

    /**
     * 查询项目成果列表（关联业务类型表）
     * @param queryWrapper 查询条件
     * @param businessCategoryMajor 业务大类
     * @param businessCategoryMinor 业务小类
     * @return 项目成果列表
     */
    List<ProjectResultVo> selectVoListWithBusinessType(@Param(Constants.WRAPPER) Wrapper<ProjectResult> queryWrapper,
                                                       @Param("businessCategoryMajor") String businessCategoryMajor,
                                                       @Param("businessCategoryMinor") String businessCategoryMinor);

    /**
     * 根据ID查询项目成果详情（关联业务类型表）
     * @param id 项目成果ID
     * @return 项目成果详情
     */
    ProjectResultVo selectVoByIdWithBusinessType(@Param("id") Long id);

    /**
     * 查询信息不完整的项目成果列表（分批查询）
     * 排除成果类型为4（事项支撑）的数据
     * @param offset 偏移量
     * @param limit 限制条数
     * @return 信息不完整的项目成果列表
     */
    List<Map<String, Object>> selectIncompleteProjectResults(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询所有信息不完整的项目成果列表
     * 排除成果类型为4（事项支撑）的数据
     * @return 信息不完整的项目成果列表
     */
    List<Map<String, Object>> selectAllIncompleteProjectResults();
}
